{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport CascaderContext from \"../context\";\nexport default function Checkbox(_ref) {\n  var _classNames;\n  var prefixCls = _ref.prefixCls,\n    checked = _ref.checked,\n    halfChecked = _ref.halfChecked,\n    disabled = _ref.disabled,\n    onClick = _ref.onClick,\n    disableCheckbox = _ref.disableCheckbox;\n  var _React$useContext = React.useContext(CascaderContext),\n    checkable = _React$useContext.checkable;\n  var customCheckbox = typeof checkable !== 'boolean' ? checkable : null;\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(\"\".concat(prefixCls), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-checked\"), checked), _defineProperty(_classNames, \"\".concat(prefixCls, \"-indeterminate\"), !checked && halfChecked), _defineProperty(_classNames, \"\".concat(prefixCls, \"-disabled\"), disabled || disableCheckbox), _classNames)),\n    onClick: onClick\n  }, customCheckbox);\n}", "map": {"version": 3, "names": ["_defineProperty", "React", "classNames", "CascaderContext", "Checkbox", "_ref", "_classNames", "prefixCls", "checked", "halfChecked", "disabled", "onClick", "disableCheckbox", "_React$useContext", "useContext", "checkable", "customCheckbox", "createElement", "className", "concat"], "sources": ["C:/Users/<USER>/Downloads/ai_attribute-weiwei/ai_attribute-weiwei/node_modules/rc-cascader/es/OptionList/Checkbox.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport CascaderContext from \"../context\";\nexport default function Checkbox(_ref) {\n  var _classNames;\n  var prefixCls = _ref.prefixCls,\n    checked = _ref.checked,\n    halfChecked = _ref.halfChecked,\n    disabled = _ref.disabled,\n    onClick = _ref.onClick,\n    disableCheckbox = _ref.disableCheckbox;\n  var _React$useContext = React.useContext(CascaderContext),\n    checkable = _React$useContext.checkable;\n  var customCheckbox = typeof checkable !== 'boolean' ? checkable : null;\n  return /*#__PURE__*/React.createElement(\"span\", {\n    className: classNames(\"\".concat(prefixCls), (_classNames = {}, _defineProperty(_classNames, \"\".concat(prefixCls, \"-checked\"), checked), _defineProperty(_classNames, \"\".concat(prefixCls, \"-indeterminate\"), !checked && halfChecked), _defineProperty(_classNames, \"\".concat(prefixCls, \"-disabled\"), disabled || disableCheckbox), _classNames)),\n    onClick: onClick\n  }, customCheckbox);\n}"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,eAAe,MAAM,YAAY;AACxC,eAAe,SAASC,QAAQA,CAACC,IAAI,EAAE;EACrC,IAAIC,WAAW;EACf,IAAIC,SAAS,GAAGF,IAAI,CAACE,SAAS;IAC5BC,OAAO,GAAGH,IAAI,CAACG,OAAO;IACtBC,WAAW,GAAGJ,IAAI,CAACI,WAAW;IAC9BC,QAAQ,GAAGL,IAAI,CAACK,QAAQ;IACxBC,OAAO,GAAGN,IAAI,CAACM,OAAO;IACtBC,eAAe,GAAGP,IAAI,CAACO,eAAe;EACxC,IAAIC,iBAAiB,GAAGZ,KAAK,CAACa,UAAU,CAACX,eAAe,CAAC;IACvDY,SAAS,GAAGF,iBAAiB,CAACE,SAAS;EACzC,IAAIC,cAAc,GAAG,OAAOD,SAAS,KAAK,SAAS,GAAGA,SAAS,GAAG,IAAI;EACtE,OAAO,aAAad,KAAK,CAACgB,aAAa,CAAC,MAAM,EAAE;IAC9CC,SAAS,EAAEhB,UAAU,CAAC,EAAE,CAACiB,MAAM,CAACZ,SAAS,CAAC,GAAGD,WAAW,GAAG,CAAC,CAAC,EAAEN,eAAe,CAACM,WAAW,EAAE,EAAE,CAACa,MAAM,CAACZ,SAAS,EAAE,UAAU,CAAC,EAAEC,OAAO,CAAC,EAAER,eAAe,CAACM,WAAW,EAAE,EAAE,CAACa,MAAM,CAACZ,SAAS,EAAE,gBAAgB,CAAC,EAAE,CAACC,OAAO,IAAIC,WAAW,CAAC,EAAET,eAAe,CAACM,WAAW,EAAE,EAAE,CAACa,MAAM,CAACZ,SAAS,EAAE,WAAW,CAAC,EAAEG,QAAQ,IAAIE,eAAe,CAAC,EAAEN,WAAW,CAAC,CAAC;IAClVK,OAAO,EAAEA;EACX,CAAC,EAAEK,cAAc,CAAC;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}