{"ast": null, "code": "import PathProxy from '../core/PathProxy.js';\nimport { applyTransform as v2ApplyTransform } from '../core/vector.js';\nvar CMD = PathProxy.CMD;\nvar points = [[], [], []];\nvar mathSqrt = Math.sqrt;\nvar mathAtan2 = Math.atan2;\nexport default function transformPath(path, m) {\n  if (!m) {\n    return;\n  }\n  var data = path.data;\n  var len = path.len();\n  var cmd;\n  var nPoint;\n  var i;\n  var j;\n  var k;\n  var p;\n  var M = CMD.M;\n  var C = CMD.C;\n  var L = CMD.L;\n  var R = CMD.R;\n  var A = CMD.A;\n  var Q = CMD.Q;\n  for (i = 0, j = 0; i < len;) {\n    cmd = data[i++];\n    j = i;\n    nPoint = 0;\n    switch (cmd) {\n      case M:\n        nPoint = 1;\n        break;\n      case L:\n        nPoint = 1;\n        break;\n      case C:\n        nPoint = 3;\n        break;\n      case Q:\n        nPoint = 2;\n        break;\n      case A:\n        var x = m[4];\n        var y = m[5];\n        var sx = mathSqrt(m[0] * m[0] + m[1] * m[1]);\n        var sy = mathSqrt(m[2] * m[2] + m[3] * m[3]);\n        var angle = mathAtan2(-m[1] / sy, m[0] / sx);\n        data[i] *= sx;\n        data[i++] += x;\n        data[i] *= sy;\n        data[i++] += y;\n        data[i++] *= sx;\n        data[i++] *= sy;\n        data[i++] += angle;\n        data[i++] += angle;\n        i += 2;\n        j = i;\n        break;\n      case R:\n        p[0] = data[i++];\n        p[1] = data[i++];\n        v2ApplyTransform(p, p, m);\n        data[j++] = p[0];\n        data[j++] = p[1];\n        p[0] += data[i++];\n        p[1] += data[i++];\n        v2ApplyTransform(p, p, m);\n        data[j++] = p[0];\n        data[j++] = p[1];\n    }\n    for (k = 0; k < nPoint; k++) {\n      var p_1 = points[k];\n      p_1[0] = data[i++];\n      p_1[1] = data[i++];\n      v2ApplyTransform(p_1, p_1, m);\n      data[j++] = p_1[0];\n      data[j++] = p_1[1];\n    }\n  }\n  path.increaseVersion();\n}", "map": {"version": 3, "names": ["PathProxy", "applyTransform", "v2ApplyTransform", "CMD", "points", "mathSqrt", "Math", "sqrt", "mathAtan2", "atan2", "transformPath", "path", "m", "data", "len", "cmd", "nPoint", "i", "j", "k", "p", "M", "C", "L", "R", "A", "Q", "x", "y", "sx", "sy", "angle", "p_1", "increaseVersion"], "sources": ["C:/Users/<USER>/Downloads/ai_attribute-weiwei/ai_attribute-weiwei/node_modules/zrender/lib/tool/transformPath.js"], "sourcesContent": ["import PathProxy from '../core/PathProxy.js';\nimport { applyTransform as v2ApplyTransform } from '../core/vector.js';\nvar CMD = PathProxy.CMD;\nvar points = [[], [], []];\nvar mathSqrt = Math.sqrt;\nvar mathAtan2 = Math.atan2;\nexport default function transformPath(path, m) {\n    if (!m) {\n        return;\n    }\n    var data = path.data;\n    var len = path.len();\n    var cmd;\n    var nPoint;\n    var i;\n    var j;\n    var k;\n    var p;\n    var M = CMD.M;\n    var C = CMD.C;\n    var L = CMD.L;\n    var R = CMD.R;\n    var A = CMD.A;\n    var Q = CMD.Q;\n    for (i = 0, j = 0; i < len;) {\n        cmd = data[i++];\n        j = i;\n        nPoint = 0;\n        switch (cmd) {\n            case M:\n                nPoint = 1;\n                break;\n            case L:\n                nPoint = 1;\n                break;\n            case C:\n                nPoint = 3;\n                break;\n            case Q:\n                nPoint = 2;\n                break;\n            case A:\n                var x = m[4];\n                var y = m[5];\n                var sx = mathSqrt(m[0] * m[0] + m[1] * m[1]);\n                var sy = mathSqrt(m[2] * m[2] + m[3] * m[3]);\n                var angle = mathAtan2(-m[1] / sy, m[0] / sx);\n                data[i] *= sx;\n                data[i++] += x;\n                data[i] *= sy;\n                data[i++] += y;\n                data[i++] *= sx;\n                data[i++] *= sy;\n                data[i++] += angle;\n                data[i++] += angle;\n                i += 2;\n                j = i;\n                break;\n            case R:\n                p[0] = data[i++];\n                p[1] = data[i++];\n                v2ApplyTransform(p, p, m);\n                data[j++] = p[0];\n                data[j++] = p[1];\n                p[0] += data[i++];\n                p[1] += data[i++];\n                v2ApplyTransform(p, p, m);\n                data[j++] = p[0];\n                data[j++] = p[1];\n        }\n        for (k = 0; k < nPoint; k++) {\n            var p_1 = points[k];\n            p_1[0] = data[i++];\n            p_1[1] = data[i++];\n            v2ApplyTransform(p_1, p_1, m);\n            data[j++] = p_1[0];\n            data[j++] = p_1[1];\n        }\n    }\n    path.increaseVersion();\n}\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,sBAAsB;AAC5C,SAASC,cAAc,IAAIC,gBAAgB,QAAQ,mBAAmB;AACtE,IAAIC,GAAG,GAAGH,SAAS,CAACG,GAAG;AACvB,IAAIC,MAAM,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACzB,IAAIC,QAAQ,GAAGC,IAAI,CAACC,IAAI;AACxB,IAAIC,SAAS,GAAGF,IAAI,CAACG,KAAK;AAC1B,eAAe,SAASC,aAAaA,CAACC,IAAI,EAAEC,CAAC,EAAE;EAC3C,IAAI,CAACA,CAAC,EAAE;IACJ;EACJ;EACA,IAAIC,IAAI,GAAGF,IAAI,CAACE,IAAI;EACpB,IAAIC,GAAG,GAAGH,IAAI,CAACG,GAAG,CAAC,CAAC;EACpB,IAAIC,GAAG;EACP,IAAIC,MAAM;EACV,IAAIC,CAAC;EACL,IAAIC,CAAC;EACL,IAAIC,CAAC;EACL,IAAIC,CAAC;EACL,IAAIC,CAAC,GAAGlB,GAAG,CAACkB,CAAC;EACb,IAAIC,CAAC,GAAGnB,GAAG,CAACmB,CAAC;EACb,IAAIC,CAAC,GAAGpB,GAAG,CAACoB,CAAC;EACb,IAAIC,CAAC,GAAGrB,GAAG,CAACqB,CAAC;EACb,IAAIC,CAAC,GAAGtB,GAAG,CAACsB,CAAC;EACb,IAAIC,CAAC,GAAGvB,GAAG,CAACuB,CAAC;EACb,KAAKT,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAED,CAAC,GAAGH,GAAG,GAAG;IACzBC,GAAG,GAAGF,IAAI,CAACI,CAAC,EAAE,CAAC;IACfC,CAAC,GAAGD,CAAC;IACLD,MAAM,GAAG,CAAC;IACV,QAAQD,GAAG;MACP,KAAKM,CAAC;QACFL,MAAM,GAAG,CAAC;QACV;MACJ,KAAKO,CAAC;QACFP,MAAM,GAAG,CAAC;QACV;MACJ,KAAKM,CAAC;QACFN,MAAM,GAAG,CAAC;QACV;MACJ,KAAKU,CAAC;QACFV,MAAM,GAAG,CAAC;QACV;MACJ,KAAKS,CAAC;QACF,IAAIE,CAAC,GAAGf,CAAC,CAAC,CAAC,CAAC;QACZ,IAAIgB,CAAC,GAAGhB,CAAC,CAAC,CAAC,CAAC;QACZ,IAAIiB,EAAE,GAAGxB,QAAQ,CAACO,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,IAAIkB,EAAE,GAAGzB,QAAQ,CAACO,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,IAAImB,KAAK,GAAGvB,SAAS,CAAC,CAACI,CAAC,CAAC,CAAC,CAAC,GAAGkB,EAAE,EAAElB,CAAC,CAAC,CAAC,CAAC,GAAGiB,EAAE,CAAC;QAC5ChB,IAAI,CAACI,CAAC,CAAC,IAAIY,EAAE;QACbhB,IAAI,CAACI,CAAC,EAAE,CAAC,IAAIU,CAAC;QACdd,IAAI,CAACI,CAAC,CAAC,IAAIa,EAAE;QACbjB,IAAI,CAACI,CAAC,EAAE,CAAC,IAAIW,CAAC;QACdf,IAAI,CAACI,CAAC,EAAE,CAAC,IAAIY,EAAE;QACfhB,IAAI,CAACI,CAAC,EAAE,CAAC,IAAIa,EAAE;QACfjB,IAAI,CAACI,CAAC,EAAE,CAAC,IAAIc,KAAK;QAClBlB,IAAI,CAACI,CAAC,EAAE,CAAC,IAAIc,KAAK;QAClBd,CAAC,IAAI,CAAC;QACNC,CAAC,GAAGD,CAAC;QACL;MACJ,KAAKO,CAAC;QACFJ,CAAC,CAAC,CAAC,CAAC,GAAGP,IAAI,CAACI,CAAC,EAAE,CAAC;QAChBG,CAAC,CAAC,CAAC,CAAC,GAAGP,IAAI,CAACI,CAAC,EAAE,CAAC;QAChBf,gBAAgB,CAACkB,CAAC,EAAEA,CAAC,EAAER,CAAC,CAAC;QACzBC,IAAI,CAACK,CAAC,EAAE,CAAC,GAAGE,CAAC,CAAC,CAAC,CAAC;QAChBP,IAAI,CAACK,CAAC,EAAE,CAAC,GAAGE,CAAC,CAAC,CAAC,CAAC;QAChBA,CAAC,CAAC,CAAC,CAAC,IAAIP,IAAI,CAACI,CAAC,EAAE,CAAC;QACjBG,CAAC,CAAC,CAAC,CAAC,IAAIP,IAAI,CAACI,CAAC,EAAE,CAAC;QACjBf,gBAAgB,CAACkB,CAAC,EAAEA,CAAC,EAAER,CAAC,CAAC;QACzBC,IAAI,CAACK,CAAC,EAAE,CAAC,GAAGE,CAAC,CAAC,CAAC,CAAC;QAChBP,IAAI,CAACK,CAAC,EAAE,CAAC,GAAGE,CAAC,CAAC,CAAC,CAAC;IACxB;IACA,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,EAAEG,CAAC,EAAE,EAAE;MACzB,IAAIa,GAAG,GAAG5B,MAAM,CAACe,CAAC,CAAC;MACnBa,GAAG,CAAC,CAAC,CAAC,GAAGnB,IAAI,CAACI,CAAC,EAAE,CAAC;MAClBe,GAAG,CAAC,CAAC,CAAC,GAAGnB,IAAI,CAACI,CAAC,EAAE,CAAC;MAClBf,gBAAgB,CAAC8B,GAAG,EAAEA,GAAG,EAAEpB,CAAC,CAAC;MAC7BC,IAAI,CAACK,CAAC,EAAE,CAAC,GAAGc,GAAG,CAAC,CAAC,CAAC;MAClBnB,IAAI,CAACK,CAAC,EAAE,CAAC,GAAGc,GAAG,CAAC,CAAC,CAAC;IACtB;EACJ;EACArB,IAAI,CAACsB,eAAe,CAAC,CAAC;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}