{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { makeInner } from '../../util/model.js';\nvar each = zrUtil.each;\nvar inner = makeInner();\n/**\r\n * @param ecModel\r\n * @param newSnapshot key is dataZoomId\r\n */\nexport function push(ecModel, newSnapshot) {\n  var storedSnapshots = getStoreSnapshots(ecModel);\n  // If previous dataZoom can not be found,\n  // complete an range with current range.\n  each(newSnapshot, function (batchItem, dataZoomId) {\n    var i = storedSnapshots.length - 1;\n    for (; i >= 0; i--) {\n      var snapshot = storedSnapshots[i];\n      if (snapshot[dataZoomId]) {\n        break;\n      }\n    }\n    if (i < 0) {\n      // No origin range set, create one by current range.\n      var dataZoomModel = ecModel.queryComponents({\n        mainType: 'dataZoom',\n        subType: 'select',\n        id: dataZoomId\n      })[0];\n      if (dataZoomModel) {\n        var percentRange = dataZoomModel.getPercentRange();\n        storedSnapshots[0][dataZoomId] = {\n          dataZoomId: dataZoomId,\n          start: percentRange[0],\n          end: percentRange[1]\n        };\n      }\n    }\n  });\n  storedSnapshots.push(newSnapshot);\n}\nexport function pop(ecModel) {\n  var storedSnapshots = getStoreSnapshots(ecModel);\n  var head = storedSnapshots[storedSnapshots.length - 1];\n  storedSnapshots.length > 1 && storedSnapshots.pop();\n  // Find top for all dataZoom.\n  var snapshot = {};\n  each(head, function (batchItem, dataZoomId) {\n    for (var i = storedSnapshots.length - 1; i >= 0; i--) {\n      batchItem = storedSnapshots[i][dataZoomId];\n      if (batchItem) {\n        snapshot[dataZoomId] = batchItem;\n        break;\n      }\n    }\n  });\n  return snapshot;\n}\nexport function clear(ecModel) {\n  inner(ecModel).snapshots = null;\n}\nexport function count(ecModel) {\n  return getStoreSnapshots(ecModel).length;\n}\n/**\r\n * History length of each dataZoom may be different.\r\n * this._history[0] is used to store origin range.\r\n */\nfunction getStoreSnapshots(ecModel) {\n  var store = inner(ecModel);\n  if (!store.snapshots) {\n    store.snapshots = [{}];\n  }\n  return store.snapshots;\n}", "map": {"version": 3, "names": ["zrUtil", "makeInner", "each", "inner", "push", "ecModel", "newSnapshot", "storedSnapshots", "getStoreSnapshots", "batchItem", "dataZoomId", "i", "length", "snapshot", "dataZoomModel", "queryComponents", "mainType", "subType", "id", "percentRange", "getPercentRange", "start", "end", "pop", "head", "clear", "snapshots", "count", "store"], "sources": ["C:/Users/<USER>/Downloads/ai_attribute-weiwei/ai_attribute-weiwei/node_modules/echarts/lib/component/dataZoom/history.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { makeInner } from '../../util/model.js';\nvar each = zrUtil.each;\nvar inner = makeInner();\n/**\r\n * @param ecModel\r\n * @param newSnapshot key is dataZoomId\r\n */\nexport function push(ecModel, newSnapshot) {\n  var storedSnapshots = getStoreSnapshots(ecModel);\n  // If previous dataZoom can not be found,\n  // complete an range with current range.\n  each(newSnapshot, function (batchItem, dataZoomId) {\n    var i = storedSnapshots.length - 1;\n    for (; i >= 0; i--) {\n      var snapshot = storedSnapshots[i];\n      if (snapshot[dataZoomId]) {\n        break;\n      }\n    }\n    if (i < 0) {\n      // No origin range set, create one by current range.\n      var dataZoomModel = ecModel.queryComponents({\n        mainType: 'dataZoom',\n        subType: 'select',\n        id: dataZoomId\n      })[0];\n      if (dataZoomModel) {\n        var percentRange = dataZoomModel.getPercentRange();\n        storedSnapshots[0][dataZoomId] = {\n          dataZoomId: dataZoomId,\n          start: percentRange[0],\n          end: percentRange[1]\n        };\n      }\n    }\n  });\n  storedSnapshots.push(newSnapshot);\n}\nexport function pop(ecModel) {\n  var storedSnapshots = getStoreSnapshots(ecModel);\n  var head = storedSnapshots[storedSnapshots.length - 1];\n  storedSnapshots.length > 1 && storedSnapshots.pop();\n  // Find top for all dataZoom.\n  var snapshot = {};\n  each(head, function (batchItem, dataZoomId) {\n    for (var i = storedSnapshots.length - 1; i >= 0; i--) {\n      batchItem = storedSnapshots[i][dataZoomId];\n      if (batchItem) {\n        snapshot[dataZoomId] = batchItem;\n        break;\n      }\n    }\n  });\n  return snapshot;\n}\nexport function clear(ecModel) {\n  inner(ecModel).snapshots = null;\n}\nexport function count(ecModel) {\n  return getStoreSnapshots(ecModel).length;\n}\n/**\r\n * History length of each dataZoom may be different.\r\n * this._history[0] is used to store origin range.\r\n */\nfunction getStoreSnapshots(ecModel) {\n  var store = inner(ecModel);\n  if (!store.snapshots) {\n    store.snapshots = [{}];\n  }\n  return store.snapshots;\n}"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,KAAKA,MAAM,MAAM,0BAA0B;AAClD,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,IAAIC,IAAI,GAAGF,MAAM,CAACE,IAAI;AACtB,IAAIC,KAAK,GAAGF,SAAS,CAAC,CAAC;AACvB;AACA;AACA;AACA;AACA,OAAO,SAASG,IAAIA,CAACC,OAAO,EAAEC,WAAW,EAAE;EACzC,IAAIC,eAAe,GAAGC,iBAAiB,CAACH,OAAO,CAAC;EAChD;EACA;EACAH,IAAI,CAACI,WAAW,EAAE,UAAUG,SAAS,EAAEC,UAAU,EAAE;IACjD,IAAIC,CAAC,GAAGJ,eAAe,CAACK,MAAM,GAAG,CAAC;IAClC,OAAOD,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAClB,IAAIE,QAAQ,GAAGN,eAAe,CAACI,CAAC,CAAC;MACjC,IAAIE,QAAQ,CAACH,UAAU,CAAC,EAAE;QACxB;MACF;IACF;IACA,IAAIC,CAAC,GAAG,CAAC,EAAE;MACT;MACA,IAAIG,aAAa,GAAGT,OAAO,CAACU,eAAe,CAAC;QAC1CC,QAAQ,EAAE,UAAU;QACpBC,OAAO,EAAE,QAAQ;QACjBC,EAAE,EAAER;MACN,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,IAAII,aAAa,EAAE;QACjB,IAAIK,YAAY,GAAGL,aAAa,CAACM,eAAe,CAAC,CAAC;QAClDb,eAAe,CAAC,CAAC,CAAC,CAACG,UAAU,CAAC,GAAG;UAC/BA,UAAU,EAAEA,UAAU;UACtBW,KAAK,EAAEF,YAAY,CAAC,CAAC,CAAC;UACtBG,GAAG,EAAEH,YAAY,CAAC,CAAC;QACrB,CAAC;MACH;IACF;EACF,CAAC,CAAC;EACFZ,eAAe,CAACH,IAAI,CAACE,WAAW,CAAC;AACnC;AACA,OAAO,SAASiB,GAAGA,CAAClB,OAAO,EAAE;EAC3B,IAAIE,eAAe,GAAGC,iBAAiB,CAACH,OAAO,CAAC;EAChD,IAAImB,IAAI,GAAGjB,eAAe,CAACA,eAAe,CAACK,MAAM,GAAG,CAAC,CAAC;EACtDL,eAAe,CAACK,MAAM,GAAG,CAAC,IAAIL,eAAe,CAACgB,GAAG,CAAC,CAAC;EACnD;EACA,IAAIV,QAAQ,GAAG,CAAC,CAAC;EACjBX,IAAI,CAACsB,IAAI,EAAE,UAAUf,SAAS,EAAEC,UAAU,EAAE;IAC1C,KAAK,IAAIC,CAAC,GAAGJ,eAAe,CAACK,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACpDF,SAAS,GAAGF,eAAe,CAACI,CAAC,CAAC,CAACD,UAAU,CAAC;MAC1C,IAAID,SAAS,EAAE;QACbI,QAAQ,CAACH,UAAU,CAAC,GAAGD,SAAS;QAChC;MACF;IACF;EACF,CAAC,CAAC;EACF,OAAOI,QAAQ;AACjB;AACA,OAAO,SAASY,KAAKA,CAACpB,OAAO,EAAE;EAC7BF,KAAK,CAACE,OAAO,CAAC,CAACqB,SAAS,GAAG,IAAI;AACjC;AACA,OAAO,SAASC,KAAKA,CAACtB,OAAO,EAAE;EAC7B,OAAOG,iBAAiB,CAACH,OAAO,CAAC,CAACO,MAAM;AAC1C;AACA;AACA;AACA;AACA;AACA,SAASJ,iBAAiBA,CAACH,OAAO,EAAE;EAClC,IAAIuB,KAAK,GAAGzB,KAAK,CAACE,OAAO,CAAC;EAC1B,IAAI,CAACuB,KAAK,CAACF,SAAS,EAAE;IACpBE,KAAK,CAACF,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;EACxB;EACA,OAAOE,KAAK,CAACF,SAAS;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}