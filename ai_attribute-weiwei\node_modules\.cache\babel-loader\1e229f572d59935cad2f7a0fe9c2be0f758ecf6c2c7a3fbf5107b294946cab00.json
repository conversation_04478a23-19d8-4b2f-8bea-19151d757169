{"ast": null, "code": "import { createContext } from '@rc-component/context';\nexport var StaticContext = createContext(null);\nexport var GridContext = createContext(null);", "map": {"version": 3, "names": ["createContext", "StaticContext", "GridContext"], "sources": ["C:/Users/<USER>/Downloads/ai_attribute-weiwei/ai_attribute-weiwei/node_modules/rc-table/es/VirtualTable/context.js"], "sourcesContent": ["import { createContext } from '@rc-component/context';\nexport var StaticContext = createContext(null);\nexport var GridContext = createContext(null);"], "mappings": "AAAA,SAASA,aAAa,QAAQ,uBAAuB;AACrD,OAAO,IAAIC,aAAa,GAAGD,aAAa,CAAC,IAAI,CAAC;AAC9C,OAAO,IAAIE,WAAW,GAAGF,aAAa,CAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}