{"ast": null, "code": "import { matcher } from \"d3-selection\";\nimport { Transition } from \"./index.js\";\nexport default function (match) {\n  if (typeof match !== \"function\") match = matcher(match);\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = [], node, i = 0; i < n; ++i) {\n      if ((node = group[i]) && match.call(node, node.__data__, i, group)) {\n        subgroup.push(node);\n      }\n    }\n  }\n  return new Transition(subgroups, this._parents, this._name, this._id);\n}", "map": {"version": 3, "names": ["matcher", "Transition", "match", "groups", "_groups", "m", "length", "subgroups", "Array", "j", "group", "n", "subgroup", "node", "i", "call", "__data__", "push", "_parents", "_name", "_id"], "sources": ["C:/Users/<USER>/Downloads/ai_attribute-weiwei/ai_attribute-weiwei/node_modules/d3-transition/src/transition/filter.js"], "sourcesContent": ["import {matcher} from \"d3-selection\";\nimport {Transition} from \"./index.js\";\n\nexport default function(match) {\n  if (typeof match !== \"function\") match = matcher(match);\n\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = [], node, i = 0; i < n; ++i) {\n      if ((node = group[i]) && match.call(node, node.__data__, i, group)) {\n        subgroup.push(node);\n      }\n    }\n  }\n\n  return new Transition(subgroups, this._parents, this._name, this._id);\n}\n"], "mappings": "AAAA,SAAQA,OAAO,QAAO,cAAc;AACpC,SAAQC,UAAU,QAAO,YAAY;AAErC,eAAe,UAASC,KAAK,EAAE;EAC7B,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAEA,KAAK,GAAGF,OAAO,CAACE,KAAK,CAAC;EAEvD,KAAK,IAAIC,MAAM,GAAG,IAAI,CAACC,OAAO,EAAEC,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAEC,SAAS,GAAG,IAAIC,KAAK,CAACH,CAAC,CAAC,EAAEI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,CAAC,EAAE,EAAEI,CAAC,EAAE;IAC9F,KAAK,IAAIC,KAAK,GAAGP,MAAM,CAACM,CAAC,CAAC,EAAEE,CAAC,GAAGD,KAAK,CAACJ,MAAM,EAAEM,QAAQ,GAAGL,SAAS,CAACE,CAAC,CAAC,GAAG,EAAE,EAAEI,IAAI,EAAEC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAE,EAAEG,CAAC,EAAE;MACnG,IAAI,CAACD,IAAI,GAAGH,KAAK,CAACI,CAAC,CAAC,KAAKZ,KAAK,CAACa,IAAI,CAACF,IAAI,EAAEA,IAAI,CAACG,QAAQ,EAAEF,CAAC,EAAEJ,KAAK,CAAC,EAAE;QAClEE,QAAQ,CAACK,IAAI,CAACJ,IAAI,CAAC;MACrB;IACF;EACF;EAEA,OAAO,IAAIZ,UAAU,CAACM,SAAS,EAAE,IAAI,CAACW,QAAQ,EAAE,IAAI,CAACC,KAAK,EAAE,IAAI,CAACC,GAAG,CAAC;AACvE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}